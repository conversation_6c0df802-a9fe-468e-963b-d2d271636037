import traceback
from typing import Dict, Any, List
from agent_server.core.api.response import StandardResponse
from fastapi import APIRouter, Depends, Body, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from agent_server.core.auth.security import check_user
from agent_server.core.vo.user_vo import UserVO
from agent_server.core.message.transmitter import Transmitter
from agent_server.core.services.database import db_manager
from agent_server.core.services.database.crud.conversation import conversation_curd
from agent_server.core.factory.agent_factory import AgentFactory
from agent_server.core.base.langchain_sender import LangchainBaseSender
from agent_server.core.base.langgraph_sender import LangGraphBaseSender
from agent_server.core.base.dify_sender import <PERSON>fy<PERSON><PERSON><PERSON><PERSON>
from agent_server.core.base.http_sender import HttpBaseSender
from agent_server.core.message.generate import (
    create_cancel_message,
    push_message_chunk,
    run_generate_message_task,
    create_or_update_conversation,
    MessageStreamHandler,
)
from agent_server.core.message.user_message import (
    save_user_message,
)
from agent_server.core.config.app_logger import logger

###################
# 大模型调用统一入口
###################
router = APIRouter(tags=["Chat"], prefix="/chat")


class CompletionReqBody(BaseModel):
    message: str
    conversation_id: str | None = None
    agent_code: str | None = None
    extend_params: Dict[str, Any] | None = None
    extend_data: Dict[str, Any] | None = None
    hidden: bool = False
    files: List[str] = []


class CancelReqBody(BaseModel):
    conversation_id: str | None = None


@router.post("/completion")
async def completion(
    request: Request,
    body: CompletionReqBody = Body(..., description="用户提问内容"),
    user: UserVO = Depends(check_user),
):
    message = body.message
    conversation_id = body.conversation_id
    agent_code = body.agent_code
    extend_params = body.extend_params
    extend_data = body.extend_data
    hidden = body.hidden
    task_id = None
    files = body.files

    async with db_manager.session() as session:
        conversation_list = await conversation_curd.get_by_conversation_id(
            session, _id=conversation_id
        )

    if conversation_list:
        task_id = conversation_list[0].task_id
        # 如果存在正在运行的任务，则直接加载消息
        # 不在这里创建对话，回复新的消息
    if not task_id:
        # 新建、更新会话
        conversation = conversation_list[0] if conversation_list else None
        (new_conversation_id, new_task_id) = await create_or_update_conversation(
            message, conversation, agent_code, user.userId
        )
        conversation_id = new_conversation_id
        task_id = new_task_id
        # 保存用户消息
        await save_user_message(
            message=message,
            agent_code=agent_code,
            conversation_id=conversation_id,
            data=extend_data,
            hidden=hidden,
            files=files,
        )

        def message_generator_fn(transmitter: Transmitter):
            sender = AgentFactory.get_sender(agent_code)
            logger.debug(f"userId:{user.userId} sender: {sender}，实例ID:{id(sender)}")

            if isinstance(
                sender,
                (
                    LangchainBaseSender,
                    DifyBaseSender,
                    HttpBaseSender,
                    LangGraphBaseSender,
                ),
            ):
                message_generator = sender.generate_stream(
                    transmitter=transmitter,
                    request=request,
                    question=message,
                    user_id=user.userId,
                    conversation_id=conversation_id,
                    extend_params=extend_params,
                    files=files,
                )
            else:
                message_generator = sender(
                    transmitter,
                    request,
                    message,
                    user.userId,
                    agent_code,
                    conversation_id,
                )
            return message_generator

        # Use the new sync task runner
        await run_generate_message_task(
            task_id,
            conversation_id,
            agent_code,
            message_generator_fn=message_generator_fn,
        )

    try:
        return StreamingResponse(
            MessageStreamHandler(
                task_id=task_id, conversation_id=conversation_id
            ).start(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            },
        )
    except Exception as e:
        # 打印完整错误堆栈到控制台（用于调试）
        traceback.print_exc()

        # 返回给前端的错误信息包含：
        error_info = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "stack_trace": traceback.format_exc(),
        }
        return {
            "code": 500,
            "message": f"调用大模型失败: {type(e).__name__} - {str(e)}",
            "error": error_info,
        }


@router.post("/cancel")
async def cancel(
    body: CancelReqBody = Body(..., description="会话id"),
    user: UserVO = Depends(check_user),
):
    conversation_id = body.conversation_id
    task_id = None
    async with db_manager.session() as session:
        conversation_list = await conversation_curd.get_by_conversation_id(
            session, _id=conversation_id
        )

    if conversation_list:
        task_id = conversation_list[0].task_id
    else:
        return StandardResponse(code=404, message="会话不存在")

    if task_id:
        push_message_chunk(key=task_id, message=create_cancel_message("User canceled."))
        async with db_manager.session() as session:
            await conversation_curd.update(
                db=session,
                db_obj=conversation_list[0],
                obj_input={"task_id": None},
            )
        return StandardResponse()
    else:
        return StandardResponse(code=404, message="任务不存在")
