from dataclasses import dataclass
import json
import time
from typing import List
import asyncio
from uuid import uuid4

from agent_server.core.message.transmitter import EVENT_TYPE_ERROR, PACKAGE_TYPE_TEXT
from agent_server.core.services.database import db_manager
from agent_server.core.services.database.crud.user_uploaded_files import (
    user_uploaded_files_crud,
)
from agent_server.core.config.app_logger import logger
from agent_server.core.config.app_config import config
from agent_server.core.services.database.schemas.user_uploaded_files import FileStatus
from agent_server.core.rag.utils import get_rag_service


@dataclass
class FileListItem:
    id: str
    name: str
    status: FileStatus
    rag_service: str
    progress: float


default_process_timeout = 60 * 10


class RAGFileMessageGenerator:
    def __init__(self, *, file_ids: List[str], check_status_interval=5):
        self.file_ids = file_ids
        self.file_list: List[FileListItem] = []
        self.state_key = "state_" + uuid4().hex
        self.check_status_interval = check_status_interval
        self.start_time = 0
        # 从配置文件读取 timeout 值
        self.process_timeout = (
            config.rag_service.process_timeout
            if config.rag_service
            else default_process_timeout
        )

    async def gen_file_list(self):
        """
        根据文件 file_ids 分别查询文件的 rag_service 属性，根据 rag_service 调用 get_rag_service() 对应的 RAGService,
        再通过 get_file_status() 方法查询文件状态，并将文件状态添加到 self.file_list 列表中。
        间隔2s轮询查询状态，直到所有文件处理完成（状态为 ready 或 error）后退出轮询。
        """
        self.start_time = time.time()
        await self._gen_initial_list()
        first_message = self._gen_first_message()
        yield {
            "data": first_message,
            "is_last": False,
            "package_type": PACKAGE_TYPE_TEXT,
            "is_new_package": True,
        }

        # 轮询直到所有文件处理完成
        while True:
            if time.time() - self.start_time > self.process_timeout:
                logger.warning(f"文件处理超时，中断轮询: {self.file_ids}")
                yield {
                    "data": "文件处理超时",
                    "is_last": True,
                    "package_type": PACKAGE_TYPE_TEXT,
                    "is_new_package": False,
                    "event_type": EVENT_TYPE_ERROR
                }
                break

            all_completed = True
            # 遍历每个文件，更新状态
            for file_item in self.file_list:
                file_id = file_item["id"]
                current_status = file_item["status"]
                rag_service_name = file_item["rag_service"]

                # 如果文件还未完成处理，查询最新状态
                if current_status not in [FileStatus.READY, FileStatus.ERROR]:
                    try:
                        # 根据 rag_service 获取对应的 RAG 服务实例
                        rag_service = get_rag_service(rag_service_name)

                        # 查询文件状态（注意：这里需要使用 rag_file_id 而不是数据库 id）
                        # 首先需要获取 rag_file_id
                        async with db_manager.session() as session:
                            file_record = await user_uploaded_files_crud.get(
                                session, file_id
                            )
                        if file_record:
                            status_info = await rag_service.get_file_status(
                                file_record.rag_file_id
                            )

                            # 更新文件状态
                            new_status = status_info.get("status", current_status)
                            file_item["status"] = new_status
                            # 更新进度
                            file_item["progress"] = status_info.get("progress", 0)

                            # 检查是否还有未完成的文件
                            if new_status not in [
                                FileStatus.READY,
                                FileStatus.ERROR,
                            ]:
                                all_completed = False

                            # 如果有错误信息，也更新
                            if "error_message" in status_info:
                                file_item["error_message"] = status_info[
                                    "error_message"
                                ]
                            else:
                                logger.warning(
                                    f"File record not found for id: {file_id}"
                                )
                                file_item["status"] = FileStatus.ERROR
                                file_item["error_message"] = "File record not found"

                    except Exception as e:
                        logger.error(f"Error checking status for file {file_id}: {e}")
                        file_item["status"] = FileStatus.ERROR
                        file_item["error_message"] = str(e)
                else:
                    # 文件已完成，不需要再检查
                    pass

            # 生成更新状态的消息
            state_message = self._gen_update_state()
            yield {
                "data": state_message,
                "is_last": False,
                "package_type": PACKAGE_TYPE_TEXT,
                "is_new_package": False,
            }

            # 如果所有文件都处理完成，退出轮询
            if all_completed:
                yield {
                    "data": "",
                    "is_last": True,
                    "package_type": PACKAGE_TYPE_TEXT,
                    "is_new_package": False,
                }
                break

            # 等待两秒继续轮询
            await asyncio.sleep(self.check_status_interval)

    async def _gen_initial_list(self):
        try:
            async with db_manager.session() as session:
                file_list = await user_uploaded_files_crud.get_by_ids(
                    session, self.file_ids
                )
            for file in file_list:
                newItem = {
                    "id": file.id,
                    "name": file.name,
                    "status": file.status,
                    "rag_service": file.rag_service,
                    "progress": 0,
                }
                self.file_list.append(newItem)
        except Exception as e:
            logger.error(f"Error during query file list: {e}")
            raise

    def _gen_first_message(self):
        template = f"""
<message-embedded>
{self._gen_initial_state()}
{self._gen_widget()}
</message-embedded>
"""
        return template

    def _gen_initial_state(self):
        template = f"""
<state>
  <set>
    <strategy>replace</strategy>
    <path>{self.state_key}</path>
    <value>[]</value>
  </set>
</state>
"""
        return template

    def _gen_widget(self):
        template = f"""
<widget>
    <code>@BuildIn/RAGFileList</code>
    <props>
        <data>{{{{state.{self.state_key}}}}}</data>
    </props>
</widget>
"""
        return template

    def _gen_update_state(self):
        data = json.dumps(self.file_list)
        template = f"""
<message-embedded>
  <state>
    <set>
      <strategy>replace</strategy>
      <path>{self.state_key}</path>
      <value>{data}</value>
    </set>
  </state>
</message-embedded>
"""
        return template
