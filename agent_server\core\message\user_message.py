from typing import Any, Dict, List
from agent_server.core.config.app_logger import logger
from agent_server.core.message.types import MessageType, MessagePackage
from agent_server.core.services.database.schemas.message import MessageCreate
from agent_server.core.services.database.crud.message import message_curd
from agent_server.core.services.database import db_manager

async def save_user_message(
    *,
    message: str,
    agent_code: str,
    conversation_id: str,
    data: Dict[str, Any],
    hidden: bool = False,
    files: List[str] = None,
) -> None:
    """保存用户消息到数据库

    将用户发送的消息保存到数据库中，包括消息内容、元数据和附件信息

    Args:
        message (str): 用户消息内容
        agent_code (str): 智能体代码
        conversation_id (str): 会话ID
        data (Dict[str, Any]): 消息相关的数据对象
        hidden (bool, optional): 是否为隐藏消息. Defaults to False.
        files (List[str], optional): 附件文件列表. Defaults to None.

    Raises:
        Exception: 数据库操作异常
    """
    if files is None:
        files = []

    logger.info(f"保存用户消息: conversation_id={conversation_id}, agent_code={agent_code}, "
               f"消息长度={len(message)}, 附件数量={len(files)}, 隐藏={hidden}")

    try:
        # 创建消息包装对象
        message_pkg = MessagePackage(
            package_id=0,
            package_type=0,
            status=1,
            data=message,
        )
        logger.debug(f"创建消息包装: {message_pkg}")

        async with db_manager.session() as session:
            # 创建消息记录
            new_message = MessageCreate(
                agent_code=agent_code,
                conversation_id=conversation_id,
                message_type=MessageType.HUMAN,
                content=[message_pkg.model_dump()],
                data_object=data,
                hidden=hidden,
                files=files,
            )

            # 保存到数据库
            saved_message = await message_curd.create(db=session, obj_input=new_message)
            logger.info(f"成功保存用户消息: message_id={saved_message.id if hasattr(saved_message, 'id') else 'unknown'}")

    except Exception as e:
        logger.error(f"保存用户消息失败: conversation_id={conversation_id}, "
                    f"agent_code={agent_code}, 错误: {str(e)}", exc_info=True)
        raise
